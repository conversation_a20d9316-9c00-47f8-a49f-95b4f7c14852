import { DeleteOutlined, DragOutlined, EyeOutlined } from '@ant-design/icons';
import { useDraggable } from '@dnd-kit/core';
import { Button, Tooltip } from 'antd';
import React, { useCallback, useMemo } from 'react';
import type { DragItem } from '.';

interface DraggableItemProps {
  item: DragItem;
  onDelete?: (itemId: string) => void;
  isDragging?: boolean;
  isSource?: boolean;
}

// 可拖拽的文件项目组件
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  onDelete,
  isDragging = false,
  isSource = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isCurrentDragging,
  } = useDraggable({
    id: item.id,
  });

  // 使用useMemo优化样式计算
  const style = useMemo(() => {
    if (!transform) return undefined;
    return {
      transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      opacity: isCurrentDragging ? 0.5 : 1,
    };
  }, [transform, isCurrentDragging]);

  // 处理预览 - 使用useCallback优化性能
  const handlePreview = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (item.url) {
      // 修复事件名称拼写错误：drog -> drag
      const event = new CustomEvent('drag-item-preview-file', {
        detail: {
          url: item.url,
          fileName: item.name,
        },
      });
      console.log('发送预览事件:', event.detail);
      window.dispatchEvent(event);
    } else {
      console.warn('文件没有URL，无法预览:', item);
    }
  }, [item.url, item.name]);

  // 处理删除
  const handleDelete = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onDelete?.(item.id);
    },
    [onDelete, item.id],
  );

  // 计算CSS类名
  const className = useMemo(() => {
    return `draggable-file-item ${isCurrentDragging ? 'dragging' : ''} ${isSource ? 'source-item' : 'target-item'
      }`;
  }, [isCurrentDragging, isSource]);

  // 避免在拖拽时渲染不必要的内容
  if (isDragging) {
    return (
      <div ref={setNodeRef} style={style} className={className}>
        <div className="file-item-content">
          <div className="drag-handle">
            <DragOutlined />
          </div>
          <div className="file-info">
            <div className="file-name">{item.name}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={setNodeRef} style={style} className={className} {...attributes} {...listeners}>
      <div className="file-item-content">
        <div className="drag-handle">
          <DragOutlined />
        </div>
        <div className="file-icon">
          <Tooltip title="预览">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={handlePreview}
              className="preview-btn"
            />
          </Tooltip>
        </div>
        <div className="file-info">
          <div className="file-name">
            <Tooltip title={item.name}>{item.name}</Tooltip>
          </div>
        </div>
        <div className="file-actions">
          {onDelete && (
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={handleDelete}
                className="delete-btn"
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default React.memo(DraggableItem);
